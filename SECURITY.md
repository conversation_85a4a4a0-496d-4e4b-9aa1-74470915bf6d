# 🔒 Security Features

Este documento descreve as funcionalidades de segurança implementadas na API de Chat em Tempo Real.

## 🎯 Funcionalidades Implementadas

### 1. JWT Refresh Tokens
- **Access Token**: 15 minutos de duração
- **Refresh Token**: 7 dias de duração
- **Armazenamento**: Cookies httpOnly para segurança
- **Renovação Automática**: Endpoint `/api/auth/refresh`

### 2. Rate Limiting
- **Global**: 100 requests por 15 minutos
- **Autenticação**: 5 tentativas por 15 minutos (apenas falhas)
- **Mensagens**: 30 mensagens por minuto
- **Operações Sensíveis**: 3 tentativas por hora

### 3. Códigos de Status HTTP Padronizados
- **401 Unauthorized**: Token inválido/expirado
- **403 Forbidden**: Refresh token inválido
- **429 Too Many Requests**: Rate limit excedido

## 🚀 Como Usar

### Autenticação com Refresh Tokens

#### 1. Login
```bash
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Resposta:**
```json
{
  "accessToken": "eyJhbGciOiJIUzI1NiIs...",
  "user": {
    "_id": "507f1f77bcf86cd799439011",
    "email": "<EMAIL>",
    "fullName": "John Doe",
    "profilePicture": ""
  }
}
```

#### 2. Refresh Token
```bash
POST /api/auth/refresh
```

**Resposta (Sucesso):**
```json
{
  "accessToken": "eyJhbGciOiJIUzI1NiIs...",
  "user": { ... }
}
```

**Resposta (Erro):**
```json
{
  "message": "Refresh token expired. Please login again.",
  "code": "REFRESH_TOKEN_EXPIRED"
}
```

### Rate Limiting

#### Headers de Resposta
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 2024-01-01T12:00:00.000Z
```

#### Resposta de Rate Limit
```json
{
  "message": "Too many requests, please try again later.",
  "retryAfter": 900,
  "limit": 100,
  "remaining": 0
}
```

## 🔧 Configuração

### Variáveis de Ambiente
```env
JWT_SECRET=your-super-secret-jwt-key-here
NODE_ENV=development
```

### Rate Limits Configurados

| Endpoint | Limite | Janela | Descrição |
|----------|--------|--------|-----------|
| Global | 100 req | 15 min | Limite geral da API |
| `/api/auth/login` | 5 falhas | 15 min | Tentativas de login |
| `/api/auth/signup` | 5 falhas | 15 min | Tentativas de registro |
| `/api/auth/refresh` | 5 req | 15 min | Renovação de tokens |
| `/api/auth/update` | 3 req | 1 hora | Atualizações de perfil |
| `/api/message/send/*` | 30 req | 1 min | Envio de mensagens |
| `/api/message/*` | 100 req | 15 min | Operações de mensagem |

## 🛡️ Códigos de Erro

### Autenticação (401)
- `TOKEN_MISSING`: Token de acesso não fornecido
- `TOKEN_INVALID`: Token de acesso inválido
- `TOKEN_EXPIRED`: Token de acesso expirado
- `USER_NOT_FOUND`: Usuário não encontrado
- `AUTH_FAILED`: Falha na autenticação

### Refresh Token (403)
- `REFRESH_TOKEN_MISSING`: Refresh token não fornecido
- `REFRESH_TOKEN_INVALID`: Refresh token inválido
- `REFRESH_TOKEN_EXPIRED`: Refresh token expirado
- `REFRESH_TOKEN_REVOKED`: Refresh token revogado

### Rate Limiting (429)
- Retorna `retryAfter` em segundos
- Headers com informações do limite

## 🔄 Fluxo de Autenticação Recomendado

1. **Login**: Obter access e refresh tokens
2. **Requisições**: Usar access token
3. **Token Expirado**: Detectar erro 401 com `TOKEN_EXPIRED`
4. **Refresh**: Chamar `/api/auth/refresh` automaticamente
5. **Retry**: Repetir requisição original com novo token
6. **Refresh Falhou**: Redirecionar para login

## 📊 Monitoramento

### Logs de Segurança
- Tentativas de login falhadas
- Rate limits excedidos
- Tokens expirados/inválidos
- Refresh tokens revogados

### Limpeza Automática
- Tokens expirados são limpos a cada hora
- Rate limit entries são limpos automaticamente

## 🧪 Testando

### Swagger UI
Acesse: `http://localhost:5001/api-docs`

### Testando Rate Limits
```bash
# Teste de rate limit de login
for i in {1..6}; do
  curl -X POST http://localhost:5001/api/auth/login \
    -H "Content-Type: application/json" \
    -d '{"email":"<EMAIL>","password":"wrong"}'
done
```

### Testando Refresh Token
```bash
# 1. Faça login primeiro
# 2. Aguarde 15 minutos (ou mude o tempo no código)
# 3. Tente acessar endpoint protegido
# 4. Use o refresh token
```

## 🚨 Considerações de Produção

Para produção, considere:

1. **Redis**: Para armazenamento de refresh tokens e rate limiting
2. **HTTPS**: Sempre usar em produção
3. **Logs**: Sistema de logs robusto
4. **Monitoring**: Alertas para tentativas de ataque
5. **Backup**: Estratégia de backup para tokens
6. **Scaling**: Rate limiting distribuído

## 📝 Notas de Desenvolvimento

- Rate limiting em memória (adequado para estudo)
- Refresh tokens em memória (usar Redis em produção)
- Limpeza automática implementada
- Códigos de erro padronizados
- Headers informativos incluídos
